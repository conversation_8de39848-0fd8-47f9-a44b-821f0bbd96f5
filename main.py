# <AUTHOR> bamtercel<PERSON>
# @Datetime : 2018/1/30 19:50
# @File : main.py
# @Last Modify Time : 2018/1/30 19:50
# @Contact : bamtercelboo@{gmail.com, 163.com}

"""
    FILE :  main.py
    FUNCTION : main
"""

import argparse
import datetime
import Config.config as configurable
from DataUtils.mainHelp import *
from test import load_test_data
from test import T_Inference
from trainer import Train
import random
import matplotlib.pyplot as plt # <--- 添加导入
import os
import shutil # <--- 确保 shutil 已导入

# solve default encoding problem
from importlib import reload
defaultencoding = 'utf-8'
if sys.getdefaultencoding() != defaultencoding:
    reload(sys)
    sys.setdefaultencoding(defaultencoding)

# random seed
torch.manual_seed(seed_num)
random.seed(seed_num)


def start_train(train_iter, dev_iter, test_iter, model, config):
    """
    :param train_iter:  train batch data iterator
    :param dev_iter:  dev batch data iterator
    :param test_iter:  test batch data iterator
    :param model:  nn model
    :param config:  config
    :return:  None
    """
    # 确保模型在正确的设备上
    model = model.to(config.device)
    t = Train(train_iter=train_iter, dev_iter=dev_iter, test_iter=test_iter, model=model, config=config)
    t.train()
    print("Finish Train.")
    if hasattr(t, 'train_loss_history') and t.train_loss_history:
        try:
            plt.figure()
            plt.plot(t.epoch_loss_history, label='Epoch Average Loss')
            plt.xlabel('Batch Number') # 记录的是每个 batch 的 loss
            plt.ylabel('Loss')
            plt.title('Training Loss Curve')
            plt.legend()
            # 确保 config.save_dir 存在且可写
            loss_plot_path = os.path.join("./", "training_loss_curve.png")
            plt.savefig(loss_plot_path)
            print(f"Training loss curve saved to {loss_plot_path}")
            # plt.show()
            plt.close() # 关闭图像，释放资源
        except Exception as e:
            print(f"Error plotting/saving loss curve: {e}")
    else:
        print("Loss history not found in Train object, skipping plot generation.")
        print("Ensure 'Train' class in 'trainer.py' stores loss values in 't.train_loss_history'.")


def start_test(train_iter, dev_iter, test_iter, model, alphabet, config):
    """
    :param train_iter:  train batch data iterator
    :param dev_iter:  dev batch data iterator
    :param test_iter:  test batch data iterator
    :param model:  nn model
    :param alphabet:  alphabet dict
    :param config:  config
    :return:  None
    """
    print("\nTesting Start......")
    data, path_source, path_result = load_test_data(train_iter, dev_iter, test_iter, config)
    infer = T_Inference(model=model, data=data, path_source=path_source, path_result=path_result, alphabet=alphabet,
                        config=config)
    infer.infer2file()
    print("Finished Test.")


def main():
    """
    main()
    :return:
    """
    # save file
    config.mulu = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
    # config.add_args(key="mulu", value=datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S'))
    config.save_dir = os.path.join(config.save_direction, config.mulu)
    if not os.path.isdir(config.save_dir): os.makedirs(config.save_dir)

    print("Copy [model/DataUtils/Dataloader/Config] file to {}".format(config.save_dir))
    shutil.copytree("Model", "/".join([config.save_dir, "Model"]))
    shutil.copytree("DataUtils", "/".join([config.save_dir, "DataUtils"]))
    shutil.copytree("Dataloader", "/".join([config.save_dir, "Dataloader"]))
    shutil.copytree("Config", "/".join([config.save_dir, "Config"]))

    # get data, iter, alphabet
    train_iter, dev_iter, test_iter, alphabet = load_data(config=config)

    # get params
    get_params(config=config, alphabet=alphabet)

    # save dictionary
    save_dictionary(config=config)

    parser = load_model(config)
    # 确保模型在正确的设备上
    parser = parser.to(config.device)

    # print("Training Start......")
    if config.train is True:
        start_train(train_iter, dev_iter, test_iter, parser, config)
        exit()
    elif config.test is True:
        print("For Future.")
        exit()
        start_test(train_iter, dev_iter, test_iter, parser, alphabet, config)
        exit()


def parse_argument():
    """
    :argument
    :return:
    """
    parser = argparse.ArgumentParser(description="PyTorch Biaffine Dependency Parsing")
    parser.add_argument("-c", "--config", dest="config_file", type=str, default="./Config/config.cfg",
                        help="config path")
    parser.add_argument("-device", "--device", dest="device", type=str, default="cpu",
                        help="device[‘cpu’,‘cuda:0’,‘cuda:1’,......]")
    parser.add_argument("--train", dest="train", action="store_true", default=True, help="train model")
    parser.add_argument("-p", "--process", dest="process", action="store_true", default=True, help="data process")
    parser.add_argument("-t", "--test", dest="test", action="store_true", default=False, help="test model")
    parser.add_argument("--t_model", dest="t_model", type=str, default=None, help="model for test")
    parser.add_argument("--t_data", dest="t_data", type=str, default=None,
                        help="data[train dev test None] for test model")
    parser.add_argument("--predict", dest="predict", action="store_true", default=False, help="predict model")
    args = parser.parse_args()
    # print(vars(args))
    config = configurable.Configurable(config_file=args.config_file)
    config.device = args.device
    config.train = args.train
    config.process = args.process
    config.test = args.test
    config.t_model = args.t_model
    config.t_data = args.t_data
    config.predict = args.predict
    # config
    if config.test is True:
        config.train = False
    if config.t_data not in [None, "train", "dev", "test"]:
        print("\nUsage")
        parser.print_help()
        print("t_data : {}, not in [None, 'train', 'dev', 'test']".format(config.t_data))
        exit()
    print("***************************************")
    print("Device : {}".format(config.device))
    print("Data Process : {}".format(config.process))
    print("Train model : {}".format(config.train))
    print("Test model : {}".format(config.test))
    print("t_model : {}".format(config.t_model))
    print("t_data : {}".format(config.t_data))
    print("predict : {}".format(config.predict))
    print("***************************************")

    return config


if __name__ == "__main__":

    print("Process ID {}, Process Parent ID {}".format(os.getpid(), os.getppid()))
    config = parse_argument()
    if config.device != cpu_device:
        print("Using GPU To Train......")
        device_number = config.device[-1]
        torch.cuda.set_device(int(device_number))
        print("Current Cuda Device {}".format(torch.cuda.current_device()))
        torch.backends.cudnn.enabled = True
        torch.backends.cudnn.deterministic = True
        torch.cuda.manual_seed(seed_num)
        torch.cuda.manual_seed_all(seed_num)
        print("torch.cuda.initial_seed", torch.cuda.initial_seed())

    main()

