[Embed]
pretrained_embed = True
zeros = False
avg = True
uniform = False
nnembed = False
pretrained_embed_file = ./Data/Embed/giga.100.txt.sample

[Data]
train_file = ./Data/ctb51/train.conll
dev_file = ./Data/ctb51/dev.conll
test_file = ./Data/ctb51/dev.conll
max_count = -1
min_freq = 1
shuffle = True
epochs_shuffle = True

[Save]
save_pkl = False
pkl_directory = ./Save_All/Save_pkl
pkl_data = pkl_data.pkl
pkl_alphabet = pkl_alphabet.pkl
pkl_iter = pkl_iter.pkl
pkl_embed = pkl_embed.pkl
save_dict = True
dict_directory = ./Save_All/Save_dictionary
word_dict = dictionary_word.txt
label_dict = dictionary_label
save_direction = ./Save_All/Save_model
save_best_model_dir = ./Save_All/Save_BModel
save_model = True
save_all_model = False
save_best_model = True
model_name = Biaffine_Parsing
rm_model = True

[Model]
embed_dim = 100
tag_dims = 100
dropout_emb = 0.5
dropout = 0.5
lstm_layers = 3
lstm_hiddens = 400
dropout_lstm_input = 0.33
dropout_lstm_hidden = 0.33
mlp_arc_size = 500
mlp_rel_size = 100
dropout_mlp = 0.33

[Optimizer]
adam = True
sgd = False
learning_rate = 0.001
weight_decay = 1e-9
clip_max_norm_use = True
clip_max_norm = 9
use_lr_decay = False
lr_rate_decay = 0.5
min_lrate = 0.000005
max_patience = 2

[Train]
num_threads = 1
epochs = 1000
early_max_patience = 2000
update_batch_size = 4
batch_size = 2
dev_batch_size = 2
test_batch_size = 2
log_interval = 1

