# <AUTHOR> bam<PERSON><PERSON><PERSON>
# @Datetime : 2018/1/30 15:55
# @File : Common.py
# @Last Modify Time : 2018/1/30 15:55
# @Contact : bamtercelboo@{gmail.com, 163.com}

"""
    FILE :  Common.py
    FUNCTION : Common File
"""

seed_num = 666
UNK = "<UNK>"
PAD = "<PAD>"

cpu_device = "cpu"


def print_common():
    """
    :return:
    """
    print("UNK", UNK)
    print("PAD", PAD)
    print("seed_num", seed_num)
    print("Cpu Device", cpu_device)

